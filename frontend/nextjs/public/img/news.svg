<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
<svg xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:cc="http://creativecommons.org/ns#"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:svg="http://www.w3.org/2000/svg"
     xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink"
     width="128" height="128" version="1.1">
    <title>News Icon</title>
    <desc>This is shape (source) for Clarity vector icon theme for gtk</desc>
    <metadata>
        <rdf:RDF>
            <cc:Work rdf:about="">
                <dc:title>News Icon</dc:title>
                <dc:description>This is shape (source) for Clarity vector icon theme for gtk</dc:description>
                <dc:creator>
                    <cc:Agent>
                        <dc:title>J<PERSON><PERSON></dc:title>
                    </cc:Agent>
                </dc:creator>
                <dc:rights>
                    <cc:Agent>
                        <dc:title>Jakub Jankiewicz</dc:title>
                    </cc:Agent>
                </dc:rights>
                <dc:date>2010</dc:date>
                <dc:format>image/svg+xml</dc:format>
                <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
                <cc:license rdf:resource="http://creativecommons.org/licenses/by-sa/3.0/" />
             </cc:Work>
        </rdf:RDF>
    </metadata>
    <path d="m 20.579809,16.574639 c -6.0667,0 -11.0164924,4.951842 -11.0164924,11.018522 v 43.600362 3.775385 20.856825 c 0,8.607747 6.9999724,15.599627 15.6077074,15.599627 h 69.80755 16.380996 c 0.8051,0 1.57683,-0.14377 2.29966,-0.3968 1.6182,-0.1179 2.87219,-1.21907 3.60739,-2.44943 0.48487,-0.81144 0.80226,-1.73899 0.98586,-2.73086 0.0177,-0.0807 0.0298,-0.16297 0.0446,-0.24484 0.025,-0.15787 0.0554,-0.31261 0.0728,-0.47369 0.025,-0.22681 0.038,-0.4577 0.0405,-0.69026 2.9e-4,-0.0274 0.004,-0.0509 0.004,-0.0768 0.004,-0.12614 0.0223,-0.2468 0.0223,-0.3745 V 52.205431 h -0.0223 V 31.563159 c 0,-3.776285 -3.02291,-6.883134 -6.76334,-7.038652 -1.33571,-4.580988 -5.57181,-7.949594 -10.57315,-7.949594 z m 0,4.147885 h 80.498121 c 3.84159,0 6.87062,3.02905 6.87062,6.870637 v 43.600362 3.775385 20.856825 h 0.0223 v 8.162177 c 0,1.17941 0.19704,2.28496 0.56478,3.28957 H 59.378421 v 0.002 H 25.171024 c -6.382642,0 -11.461848,-5.07113 -11.461848,-11.453784 V 74.968908 71.193523 27.593161 c 0,-3.841587 3.02903,-6.870637 6.870633,-6.870637 z m 3.014251,6.793701 c -1.650383,0 -2.977816,2.223996 -2.977816,4.985962 v 20.871005 c 0,2.761965 1.327433,4.985962 2.977816,4.985962 h 25.401491 c 1.650383,0 2.979836,-2.223997 2.979836,-4.985962 V 32.502187 c 0,-2.761966 -1.329453,-4.985962 -2.979836,-4.985962 z m 43.316952,0 c -1.65038,0 -2.977812,1.380787 -2.977812,3.095215 0,1.714466 1.327432,3.093216 2.977812,3.093216 h 31.579795 c 1.650383,0 2.979843,-1.37875 2.979843,-3.093216 0,-1.714428 -1.32946,-3.095215 -2.979843,-3.095215 z m 45.183398,1.228755 c 1.27215,0.314955 2.17415,1.425444 2.17415,2.817905 v 72.771175 c -10e-4,0.0274 -0.004,0.058 -0.006,0.087 -0.002,0.14611 -0.0184,0.28792 -0.0405,0.4271 -0.0112,0.0741 -0.027,0.14142 -0.0405,0.21272 -0.17268,0.74308 -0.61139,1.36221 -1.22472,1.75512 -0.0623,-0.0642 -0.15058,-0.17276 -0.26521,-0.36431 -0.29929,-0.50087 -0.58098,-1.41506 -0.58098,-2.46362 V 52.205274 h -0.0162 z M 66.911012,39.842474 c -1.65038,0 -2.977812,1.380747 -2.977812,3.095215 0,1.714466 1.327432,3.095214 2.977812,3.095214 h 31.579795 c 1.650383,0 2.979843,-1.380748 2.979843,-3.095214 0,-1.714468 -1.32946,-3.095215 -2.979843,-3.095215 z m 0,12.326212 c -1.65038,0 -2.977812,1.380786 -2.977812,3.095253 0,1.714428 1.327432,3.095215 2.977812,3.095215 h 31.579795 c 1.650383,0 2.979843,-1.380787 2.979843,-3.095215 0,-1.714467 -1.32946,-3.095253 -2.979843,-3.095253 z M 23.648714,68.11245 c -1.679649,0 -3.03247,1.378751 -3.03247,3.093217 0,1.714428 1.352821,3.095215 3.03247,3.095215 h 74.789467 c 1.679649,0 3.032469,-1.380787 3.032469,-3.095215 0,-1.714466 -1.35282,-3.093217 -3.032469,-3.093217 z m 0.06276,13.508469 c -1.714451,0 -3.095226,1.378749 -3.095226,3.093177 0,1.714467 1.380775,3.095253 3.095226,3.095253 h 25.168695 c 1.714447,0 3.093197,-1.380786 3.093197,-3.095253 0,-1.714428 -1.37875,-3.093177 -3.093197,-3.093177 z m 43.199542,0 c -1.65038,0 -2.977812,1.378749 -2.977812,3.093177 0,1.714467 1.327432,3.095253 2.977812,3.095253 h 31.57979 c 1.650384,0 2.979844,-1.380786 2.979844,-3.095253 0,-1.714428 -1.32946,-3.093177 -2.979844,-3.093177 z m -43.199547,13.50643 c -1.714451,0 -3.095226,1.380786 -3.095226,3.09521 0,1.714472 1.380775,3.095251 3.095226,3.095251 h 25.168695 c 1.714447,0 3.093197,-1.380779 3.093197,-3.095251 0,-1.714424 -1.37875,-3.09521 -3.093197,-3.09521 z m 43.199542,0 c -1.65038,0 -2.977812,1.380786 -2.977812,3.09521 0,1.714472 1.327432,3.095251 2.977812,3.095251 h 31.579795 c 1.650384,0 2.979844,-1.380779 2.979844,-3.095251 0,-1.714424 -1.32946,-3.09521 -2.979844,-3.09521 z"/>
</svg>