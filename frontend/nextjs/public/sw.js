if(!self.define){let e,s={};const i=(i,a)=>(i=new URL(i+".js",a).href,s[i]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()}).then(()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(a,c)=>{const n=e||("document"in self?document.currentScript.src:"")||location.href;if(s[n])return;let r={};const t=e=>i(e,n),f={module:{uri:n},exports:r,require:t};s[n]=Promise.all(a.map(e=>f[e]||t(e))).then(e=>(c(...e),r))}}define(["./workbox-f1770938"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/static/G7IT8vcClgLfP_ydxxfW4/_buildManifest.js",revision:"c155cce658e53418dec34664328b51ac"},{url:"/_next/static/G7IT8vcClgLfP_ydxxfW4/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/280-320817e2ffab6569.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/434-baf8a8125c039bd0.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/560-d8dd596ffbff5b0a.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/653-5af4e210b86e256b.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/745-bbb285dc1df000c1.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/752-7cb70f1daee5e32d.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/997-79a992dba56a097f.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/app/_not-found/page-522e51c1efb44c8d.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/app/layout-ddf6317a96bddea3.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/app/page-e31dbd7384863db3.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/app/research/%5Bid%5D/page-0090a3dac8c1fda7.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/fd9d1056-4a9853f3fae16b4a.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/framework-f66176bb897dc684.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/main-512b1b24104322c5.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/main-app-6942d00797161b14.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/pages/_app-72b849fbd24ac258.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/pages/_error-7ba65e1336b92748.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-bf8edad7b7764fb9.js",revision:"G7IT8vcClgLfP_ydxxfW4"},{url:"/_next/static/css/154871ea4058421a.css",revision:"154871ea4058421a"},{url:"/_next/static/css/29886ec2f5f5c8be.css",revision:"29886ec2f5f5c8be"},{url:"/_next/static/media/630e0b819503bca7-s.woff2",revision:"e3c313092df5d8ea3306d6b29bd44c00"},{url:"/_next/static/media/6eed223b32d97b82-s.woff2",revision:"a981ad4865c753b27f8c8e6feaaf8875"},{url:"/_next/static/media/793968fa3513f5d6-s.p.woff2",revision:"7e692144d823ca28415d410ba874b188"},{url:"/embed.js",revision:"0b3e41a99c8dfc5f1d62d0d671dda685"},{url:"/favicon.ico",revision:"7b22b406f80ee676dad48471e11227db"},{url:"/img/F.svg",revision:"864e14d8969075afeb81193720a5d81a"},{url:"/img/Info.svg",revision:"103713a9bf38d3899089f3a183465c74"},{url:"/img/W.svg",revision:"29ffc7f7899d212c10511aff379e5d28"},{url:"/img/agents/academicResearchAgentAvatar.png",revision:"5fdd1619642f9bcc42052fda16d9be2f"},{url:"/img/agents/businessAnalystAgentAvatar.png",revision:"c1c70436702666ca929509a43f3e8463"},{url:"/img/agents/computerSecurityanalystAvatar.png",revision:"c0b2092292bee3ee10b9142d8e176651"},{url:"/img/agents/defaultAgentAvatar.JPG",revision:"31e5569b41a8a71950bd11d10902d387"},{url:"/img/agents/financeAgentAvatar.png",revision:"8a2abf6022b4370c173ab5d640aee1f7"},{url:"/img/agents/mathAgentAvatar.png",revision:"158c0f4efeef63eea09970468a27012b"},{url:"/img/agents/travelAgentAvatar.png",revision:"bebef00873137dbff42c48a88ab68742"},{url:"/img/arrow-circle-up-right.svg",revision:"a5499f058a9d3691da804375c8e79516"},{url:"/img/arrow-narrow-right.svg",revision:"cd322b0eff9577fd0a09df5bb15ea8b1"},{url:"/img/browser.svg",revision:"3b8d111e94d135b5f9cffdfe6f132e34"},{url:"/img/chat-check.svg",revision:"d32099687f04f71e26271cd43cea3e93"},{url:"/img/chat.svg",revision:"f168b490d8217a2c2aeea457cd753e02"},{url:"/img/copy-white.svg",revision:"f2f284d5666bcc2da90e44a9e8989e3f"},{url:"/img/copy.svg",revision:"8d6ebbbc4873cc44e799c95f1a895dd5"},{url:"/img/dinosaur.svg",revision:"42e67ed2261ed92c9f76c159f0f258eb"},{url:"/img/discord.svg",revision:"5db80289a6e3af4ac7c6b6d4f0cdb554"},{url:"/img/docker-blue.svg",revision:"2904f8f3306ccf8037838262c45c24fc"},{url:"/img/docker.svg",revision:"f1e915ecef483e3bcc09c7353b8888a3"},{url:"/img/dunk.svg",revision:"b9d0c2dc95f767be7fdff0dc2689ec79"},{url:"/img/github-blue.svg",revision:"af3c1a6a8d0f9d7ec3cb9102b4048d99"},{url:"/img/github-footer.svg",revision:"d79823a21fbc7cc9917ac048a12e058a"},{url:"/img/github.svg",revision:"cc1a70d86f1925dc55b2a785587306c6"},{url:"/img/globe.svg",revision:"8fcfc462e0ff5e900ba8b4d006c9f680"},{url:"/img/gptr-black-logo.png",revision:"e17c36ddf6422aa3da2c70c8f51a9ab2"},{url:"/img/gptr-logo.png",revision:"1a143f183bfdc5dd622cb97326e32127"},{url:"/img/hiker.svg",revision:"f5500bb2627cd1e8007adc8072e8f48c"},{url:"/img/icon _atom_.svg",revision:"2f9ed381dc5036721f07fd49b173db9e"},{url:"/img/icon _dumbell_.svg",revision:"5f1dc06c26e33ca171f9c3318fc671ec"},{url:"/img/icon _leaf_.svg",revision:"111e97dee61cda7a8f97546be04f4797"},{url:"/img/image.svg",revision:"dec61be5c852ca6f27d7239e560e1185"},{url:"/img/indeed.svg",revision:"3efe5203c66766eefbb1c0c88b8b232d"},{url:"/img/link.svg",revision:"882ca6e69d6766c8d3970ed21a5ad3eb"},{url:"/img/message-question-circle.svg",revision:"d9403628fbc92456319e6760a29c795e"},{url:"/img/news.svg",revision:"b6928f2e0d6629fd7cd6e8cddec3aab9"},{url:"/img/search.svg",revision:"f82a218633229f8f820e669c3e094702"},{url:"/img/share.svg",revision:"1caaaa9c8251e1f8df78be44c0081216"},{url:"/img/similarTopics.svg",revision:"b0c4231a0b55d196586671632246b737"},{url:"/img/sources.svg",revision:"3e49c70e1ed372d878ef655e837b11ce"},{url:"/img/stock.svg",revision:"ae349f42cda11d3740feeb7f55090fe9"},{url:"/img/stock2.svg",revision:"2c13e5dcf1a278719062c0cc55f44d83"},{url:"/img/thinking.svg",revision:"cc051305099b3ad03476c735e5fcfd9f"},{url:"/img/white-books.svg",revision:"4730bdc4ebc81e77491db322ef1a57db"},{url:"/img/x.svg",revision:"31c64760085fa37405d771e179a4bdd0"},{url:"/manifest.json",revision:"75ef09fbbe2160222fffe8292d63cdd0"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/vercel.svg",revision:"61c6b19abff40ea7acd577be818f3976"}],{ignoreURLParametersMatching:[/^utm_/,/^fbclid$/]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({response:e})=>e&&"opaqueredirect"===e.type?new Response(e.body,{status:200,statusText:"OK",headers:e.headers}):e}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:2592e3})]}),"GET"),e.registerRoute(/\/_next\/static.+\.js$/i,new e.CacheFirst({cacheName:"next-static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4|webm)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:48,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({sameOrigin:e,url:{pathname:s}})=>!(!e||s.startsWith("/api/auth/callback")||!s.startsWith("/api/")),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({request:e,url:{pathname:s},sameOrigin:i})=>"1"===e.headers.get("RSC")&&"1"===e.headers.get("Next-Router-Prefetch")&&i&&!s.startsWith("/api/"),new e.NetworkFirst({cacheName:"pages-rsc-prefetch",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({request:e,url:{pathname:s},sameOrigin:i})=>"1"===e.headers.get("RSC")&&i&&!s.startsWith("/api/"),new e.NetworkFirst({cacheName:"pages-rsc",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:{pathname:e},sameOrigin:s})=>s&&!e.startsWith("/api/"),new e.NetworkFirst({cacheName:"pages",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({sameOrigin:e})=>!e,new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
