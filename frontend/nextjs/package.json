{"name": "gpt-researcher-ui", "description": "GPT Researcher frontend as a React component", "version": "0.1.74", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "styles/*", "app/globals.css", "components/Settings/App.css"], "private": false, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:lib": "rollup -c", "build:types": "cp src/index.d.ts dist/", "dev:lib": "rollup -c -w"}, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@langchain/langgraph-sdk": "^0.0.1-rc.12", "@mozilla/readability": "^0.5.0", "@next/third-parties": "^15.1.6", "axios": "^1.3.2", "date-fns": "^4.1.0", "eventsource-parser": "^1.1.2", "framer-motion": "^9.0.2", "geist": "^1.3.1", "next": "^14.2.28", "next-plausible": "^3.12.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-dropzone": "^14.2.3", "react-ga4": "^2.1.0", "react-hot-toast": "^2.4.1", "rehype-prism-plus": "^2.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-parse": "^11.0.0", "zod": "^3.0.0", "zod-to-json-schema": "^3.23.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-syntax-flow": "^7.26.0", "@babel/plugin-transform-typescript": "^7.26.8", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-typescript": "^12.1.2", "@tailwindcss/typography": "^0.5.16", "@types/jsdom": "^21.1.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.0", "react-ga4": "^2.1.0", "rollup": "^2.79.2", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "tailwindcss": "^3.4.1", "typescript": "^5", "@ducanh2912/next-pwa": "^10.0.1"}, "repository": {"type": "git", "url": "git+https://github.com/assafelovic/gpt-researcher.git"}, "keywords": ["gpt", "researcher", "ai", "react", "nextjs"], "author": "GPT Researcher Team", "license": "MIT", "bugs": {"url": "https://github.com/assafelovic/gpt-researcher/issues"}, "homepage": "https://github.com/assafelovic/gpt-researcher#readme"}