{"name": "website", "version": "0.0.0", "private": true, "resolutions": {"nth-check": "2.0.1", "trim": "0.0.3", "got": "11.8.5", "node-forge": "1.3.0", "minimatch": "3.0.5", "loader-utils": "2.0.4", "eta": "2.0.0", "@sideway/formula": "3.0.1", "http-cache-semantics": "4.1.1"}, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "3.7.0", "@docusaurus/preset-classic": "3.7.0", "@easyops-cn/docusaurus-search-local": "^0.49.2", "@mdx-js/react": "^3.1.0", "@svgr/webpack": "^8.1.0", "clsx": "^1.1.1", "file-loader": "^6.2.0", "hast-util-is-element": "1.1.0", "minimatch": "3.0.5", "react": "^18.0.1", "react-dom": "^18.0.1", "rehype-katex": "^7.0.1", "remark-math": "3", "trim": "^0.0.3", "url-loader": "^4.1.1"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}