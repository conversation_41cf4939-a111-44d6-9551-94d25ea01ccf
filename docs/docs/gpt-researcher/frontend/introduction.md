# Intro to the Frontends

The frontends enhance GPT-Researcher by providing:

1. Intuitive Research Interface: Streamlined input for research queries.
2. Real-time Progress Tracking: Visual feedback on ongoing research tasks.
3. Interactive Results Display: Easy-to-navigate presentation of findings.
4. Customizable Settings: Adjust research parameters to suit specific needs.
5. Responsive Design: Optimal experience across various devices.

These features aim to make the research process more efficient and user-friendly, complementing GPT-Researcher's powerful agent capabilities.

## Choosing an Option

- Static Frontend: Quick setup, lightweight deployment.
- NextJS Frontend: Feature-rich, scalable, better performance and SEO (For production, NextJS is recommended)
- Discord Bot: Integrate GPT-Researcher into your Discord server.