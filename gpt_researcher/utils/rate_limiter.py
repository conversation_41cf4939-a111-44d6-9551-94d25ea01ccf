"""
Rate limiter for API calls with async support.
Implements 1 request per 1.2 seconds limit (configurable).
"""

import asyncio
import time
from typing import Optional
from collections import deque
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """
    Async rate limiter that enforces request rate limits.
    Thread-safe for async operations.
    """

    def __init__(self, requests_per_second: float = 0.833):
        """
        Initialize rate limiter.
        
        Args:
            requests_per_second: Maximum requests per second (default: 0.833 = 1/1.2)
        """
        self.requests_per_second = requests_per_second
        self.min_interval = 1.0 / requests_per_second if requests_per_second > 0 else 0
        self.last_request_time: Optional[float] = None
        self.request_times: deque = deque(maxlen=100)
        self._lock = asyncio.Lock()
        
        logger.info(
            f"RateLimiter initialized: {requests_per_second:.3f} req/s "
            f"(min interval: {self.min_interval:.2f}s)"
        )

    async def acquire(self):
        """
        Acquire permission to make a request.
        Blocks until request can be made within rate limits.
        """
        async with self._lock:
            current_time = time.time()
            
            if self.last_request_time is not None:
                time_since_last = current_time - self.last_request_time
                
                if time_since_last < self.min_interval:
                    sleep_time = self.min_interval - time_since_last
                    logger.debug(f"Rate limit: sleeping for {sleep_time:.3f}s")
                    await asyncio.sleep(sleep_time)
                    current_time = time.time()
            
            self.last_request_time = current_time
            self.request_times.append(current_time)

    def get_stats(self) -> dict:
        """Get current rate limiter statistics."""
        current_time = time.time()
        one_minute_ago = current_time - 60
        
        recent_requests = sum(1 for t in self.request_times if t > one_minute_ago)
        
        return {
            "requests_per_second_limit": self.requests_per_second,
            "min_interval_seconds": self.min_interval,
            "requests_last_minute": recent_requests,
            "last_request_time": self.last_request_time,
        }
