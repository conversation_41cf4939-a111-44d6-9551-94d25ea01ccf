"""
Embedding Manager - Unified interface for multiple embedding providers.
"""

import asyncio
from typing import List, Optional
import logging

from ..config.config import config
from .providers.openai_embeddings import OpenAIEmbeddings
from .providers.google_embeddings import GoogleAIEmbeddings
from .providers.mistral_embeddings import MistralEmbeddings
from .providers.huggingface_embeddings import HuggingFaceEmbeddings
from ..utils.rate_limiter import RateLimiter

logger = logging.getLogger(__name__)


class EmbeddingManager:
    """
    Manages embedding generation across multiple providers.
    Includes rate limiting and error handling.
    """

    def __init__(self, provider_name: Optional[str] = None):
        """
        Initialize embedding manager.
        
        Args:
            provider_name: Provider to use (defaults to config)
        """
        self.provider_name = provider_name or config.embedding_provider
        self.embedding_config = config.get_embedding_config()
        
        # Initialize provider
        self.provider = self._create_provider()
        
        # Initialize rate limiter
        rate_limit = self.embedding_config.get("rate_limit", 0.833)
        self.rate_limiter = RateLimiter(requests_per_second=rate_limit)
        
        logger.info(f"Initialized EmbeddingManager with provider: {self.provider_name}")

    def _create_provider(self):
        """Create the appropriate embedding provider."""
        providers = {
            "openai": OpenAIEmbeddings,
            "google_ai": GoogleAIEmbeddings,
            "mistral": MistralEmbeddings,
            "huggingface": HuggingFaceEmbeddings,
        }
        
        provider_class = providers.get(self.provider_name)
        if not provider_class:
            logger.warning(
                f"Unknown provider '{self.provider_name}', falling back to OpenAI"
            )
            provider_class = OpenAIEmbeddings
        
        return provider_class(self.embedding_config)

    async def embed_text(self, text: str) -> List[float]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        # Apply rate limiting
        if config.rate_limit_enabled:
            await self.rate_limiter.acquire()
        
        try:
            return await self.provider.embed_text(text)
        except Exception as e:
            logger.error(f"Error embedding text with {self.provider_name}: {e}")
            raise

    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        embeddings = []
        
        for text in texts:
            # Rate limiting applied per request
            if config.rate_limit_enabled:
                await self.rate_limiter.acquire()
            
            try:
                embedding = await self.provider.embed_text(text)
                embeddings.append(embedding)
            except Exception as e:
                logger.error(f"Error embedding text with {self.provider_name}: {e}")
                # Return zero vector on error
                embeddings.append([0.0] * self.embedding_config.get("dimension", 1536))
        
        return embeddings

    async def embed_documents(
        self, 
        documents: List[str], 
        batch_size: int = 10
    ) -> List[List[float]]:
        """
        Generate embeddings for documents with batching.
        
        Args:
            documents: List of documents
            batch_size: Batch size for processing
            
        Returns:
            List of embedding vectors
        """
        all_embeddings = []
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batch_embeddings = await self.embed_texts(batch)
            all_embeddings.extend(batch_embeddings)
            
            logger.debug(
                f"Embedded batch {i//batch_size + 1}/"
                f"{(len(documents)-1)//batch_size + 1}"
            )
        
        return all_embeddings


# Global embedding manager instance
_global_embedding_manager = None


def get_embedding_manager(provider_name: Optional[str] = None) -> EmbeddingManager:
    """
    Get or create global embedding manager instance.
    
    Args:
        provider_name: Provider to use (optional)
        
    Returns:
        EmbeddingManager instance
    """
    global _global_embedding_manager
    
    if _global_embedding_manager is None or provider_name:
        _global_embedding_manager = EmbeddingManager(provider_name)
    
    return _global_embedding_manager

