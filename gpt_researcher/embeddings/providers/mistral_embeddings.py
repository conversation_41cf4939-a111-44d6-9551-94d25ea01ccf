"""
Mistral AI embeddings provider.
"""

from typing import List
import logging
from mistralai.async_client import MistralAsyncClient

logger = logging.getLogger(__name__)


class MistralEmbeddings:
    """Mistral AI embeddings provider."""

    def __init__(self, config: dict):
        """
        Initialize Mistral embeddings.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.client = MistralAsyncClient(api_key=config["api_key"])
        self.model = config["model"]
        
        logger.info(f"Initialized Mistral embeddings with model: {self.model}")

    async def embed_text(self, text: str) -> List[float]:
        """Generate embedding for text."""
        try:
            response = await self.client.embeddings(
                model=self.model,
                input=[text],
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Mistral embedding error: {e}")
            raise
