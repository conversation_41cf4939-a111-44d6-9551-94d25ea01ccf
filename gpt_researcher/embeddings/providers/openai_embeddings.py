"""
OpenAI embeddings provider.
"""

import asyncio
from typing import List
import logging
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)


class OpenAIEmbeddings:
    """OpenAI embeddings provider."""

    def __init__(self, config: dict):
        """
        Initialize OpenAI embeddings.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.client = AsyncOpenAI(
            api_key=config["api_key"],
            base_url=config.get("base_url"),
            timeout=60.0,
        )
        self.model = config["model"]
        
        logger.info(f"Initialized OpenAI embeddings with model: {self.model}")

    async def embed_text(self, text: str) -> List[float]:
        """Generate embedding for text."""
        try:
            response = await self.client.embeddings.create(
                model=self.model,
                input=text,
                encoding_format="float",
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"OpenAI embedding error: {e}")
            raise
