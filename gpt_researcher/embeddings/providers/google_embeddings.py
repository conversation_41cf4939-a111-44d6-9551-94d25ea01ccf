"""
Google AI (Gemini) embeddings provider.
"""

import asyncio
from typing import List
import logging
import google.generativeai as genai

logger = logging.getLogger(__name__)


class GoogleAIEmbeddings:
    """Google AI embeddings provider."""

    def __init__(self, config: dict):
        """
        Initialize Google AI embeddings.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        genai.configure(api_key=config["api_key"])
        self.model = config["model"]
        
        logger.info(f"Initialized Google AI embeddings with model: {self.model}")

    async def embed_text(self, text: str) -> List[float]:
        """Generate embedding for text."""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: genai.embed_content(
                    model=self.model,
                    content=text,
                    task_type="retrieval_document",
                )
            )
            return result["embedding"]
        except Exception as e:
            logger.error(f"Google AI embedding error: {e}")
            raise
