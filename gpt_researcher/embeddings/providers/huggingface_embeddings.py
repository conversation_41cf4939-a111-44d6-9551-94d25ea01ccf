"""
HuggingFace embeddings provider.
"""

from typing import List
import logging
import httpx

logger = logging.getLogger(__name__)


class HuggingFaceEmbeddings:
    """HuggingFace embeddings provider."""

    def __init__(self, config: dict):
        """
        Initialize HuggingFace embeddings.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_url = f"{config['api_base']}/{config['model']}"
        self.headers = {"Authorization": f"Bearer {config['api_key']}"}
        
        logger.info(f"Initialized HuggingFace embeddings with model: {config['model']}")

    async def embed_text(self, text: str) -> List[float]:
        """Generate embedding for text."""
        try:
            payload = {
                "inputs": text,
                "options": {"wait_for_model": True}
            }
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    self.api_url,
                    json=payload,
                    headers=self.headers
                )
                response.raise_for_status()
                result = response.json()
            
            # Handle different response formats
            if isinstance(result, list):
                if isinstance(result[0], list):
                    return result[0]
                else:
                    return result
            else:
                raise ValueError(f"Unexpected response format: {type(result)}")
                
        except Exception as e:
            logger.error(f"HuggingFace embedding error: {e}")
            raise
