OPENAI_API_KEY=
OPENAI_BASE_URL=
TAVILY_API_KEY=
DOC_PATH=./my-docs

# NEXT_PUBLIC_GPTR_API_URL=http://0.0.0.0:8000  # Defaults to localhost:8000 if not set

# ===================================================================
# EXISTING CONFIGURATION (PRESERVED)
# ===================================================================

# LLM Provider Configuration
LLM_PROVIDER=openai
FAST_LLM_MODEL=gpt-3.5-turbo
SMART_LLM_MODEL=gpt-4o
STRATEGIC_LLM_MODEL=gpt-4o
TEMPERATURE=0.6

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Retriever Configuration
RETRIEVER=tavily
TAVILY_API_KEY=your_tavily_api_key_here

# ===================================================================
# NEW: EMBEDDING PROVIDER CONFIGURATION
# ===================================================================

# Embedding Provider Selection
# Options: openai, google_ai, mistral, huggingface
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-large
EMBEDDING_DIMENSION=1536

# Google AI Embeddings
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_EMBEDDING_MODEL=models/embedding-001

# Mistral AI Embeddings
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_EMBEDDING_MODEL=mistral-embed

# HuggingFace Embeddings
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
HUGGINGFACE_EMBEDDING_MODEL=sentence-transformers/all-mpnet-base-v2
HUGGINGFACE_API_BASE=https://api-inference.huggingface.co/pipeline/feature-extraction

# ===================================================================
# NEW: RATE LIMITING CONFIGURATION
# ===================================================================

# Global Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_SECOND=0.833  # 1 request per 1.2 seconds

# Per-Provider Rate Limits (Optional Overrides)
OPENAI_RATE_LIMIT=0.833
GOOGLE_RATE_LIMIT=0.833
MISTRAL_RATE_LIMIT=0.833
HUGGINGFACE_RATE_LIMIT=0.833

